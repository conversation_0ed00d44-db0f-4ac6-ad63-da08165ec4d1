const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGO_URI || "mongodb+srv://amriteshk778:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function testNewStructure() {
    const client = new MongoClient(uri);
    
    try {
        console.log('🔄 Connecting to MongoDB...');
        await client.connect();
        console.log('✅ Connected successfully to MongoDB!');
        
        const db = client.db('giggle');
        
        console.log('\n🧪 Testing New Seeker/Provider Structure...');
        
        // Clean up any existing test data
        await db.collection('authSessions').deleteMany({ email: { $regex: 'test.*@example.com' } });
        await db.collection('providers').deleteMany({ email: { $regex: 'test.*@example.com' } });
        await db.collection('seekers').deleteMany({ email: { $regex: 'test.*@example.com' } });
        await db.collection('companies').deleteMany({ companyName: { $regex: 'Test.*Company' } });
        await db.collection('gigs').deleteMany({ title: { $regex: 'Test.*Job' } });
        await db.collection('gig_applications').deleteMany({ coverLetter: { $regex: 'Test.*application' } });
        
        // Test 1: Create Provider Profile
        console.log('\n👔 Test 1: Provider Profile Creation');
        
        // Create auth session for provider
        const providerAuthSession = {
            appwriteId: 'provider_test_123',
            email: '<EMAIL>',
            name: 'Test Provider',
            selectedProfileType: 'provider',
            lastLoginAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const authResult1 = await db.collection('authSessions').insertOne(providerAuthSession);
        console.log(`   ✅ Created provider auth session: ${authResult1.insertedId}`);
        
        // Create provider profile
        const providerData = {
            appwriteId: 'provider_test_123',
            email: '<EMAIL>',
            name: 'Test Provider',
            phoneNumber: '+**********',
            phoneVerified: true,
            roleTitle: 'Business Owner',
            profilePhoto: 'https://example.com/provider-photo.jpg',
            termsAccepted: true,
            privacyPolicyAccepted: true,
            companyAddress: {
                city: 'Mumbai',
                state: 'Maharashtra',
                country: 'India',
                fullOperationalAddress: '123 Business District, Andheri East',
                pincode: 400069,
                companyEmail: '<EMAIL>',
                companyPhone: ************,
                website: 'https://testprovider.com',
                socialMediaLinks: {
                    linkedin: 'https://linkedin.com/company/testprovider',
                    twitter: 'https://twitter.com/testprovider'
                }
            },
            branding: {
                companyLogo: 'https://example.com/company-logo.png',
                businessRegistrationNumber: 'REG123456789',
                gstinOrMsmeId: 'GSTIN_TEST123',
                uploadedDocuments: ['doc1', 'doc2'],
                requestVerifiedBadge: true
            },
            verificationStatus: 'verified',
            verificationRequestedAt: new Date(),
            verificationCompletedAt: new Date(),
            profileCompletionSteps: {
                personalAccount: true,
                companyAddress: true,
                brandingVerification: true
            },
            profileCompletionPercentage: 100,
            isActive: true,
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const providerResult = await db.collection('providers').insertOne(providerData);
        console.log(`   ✅ Created provider profile: ${providerResult.insertedId}`);
        
        // Update auth session with provider ID
        await db.collection('authSessions').updateOne(
            { appwriteId: 'provider_test_123' },
            { $set: { providerId: providerResult.insertedId } }
        );
        
        // Test 2: Create Seeker Profile
        console.log('\n🔍 Test 2: Seeker Profile Creation');
        
        // Create auth session for seeker
        const seekerAuthSession = {
            appwriteId: 'seeker_test_456',
            email: '<EMAIL>',
            name: 'Test Seeker',
            selectedProfileType: 'seeker',
            lastLoginAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const authResult2 = await db.collection('authSessions').insertOne(seekerAuthSession);
        console.log(`   ✅ Created seeker auth session: ${authResult2.insertedId}`);
        
        // Create seeker profile
        const seekerData = {
            appwriteId: 'seeker_test_456',
            email: '<EMAIL>',
            name: 'Test Seeker',
            phoneNumber: '+**********',
            phoneVerified: true,
            profilePicture: 'https://example.com/seeker-photo.jpg',
            bio: 'Experienced software developer looking for new opportunities',
            dateOfBirth: new Date('1990-01-01'),
            gender: 'prefer_not_to_say',
            resumeLink: 'https://example.com/resume.pdf',
            educationLevel: 'bachelor',
            employmentStatus: 'employed',
            education: [{
                institution: 'Test University',
                degree: 'Bachelor of Technology',
                fieldOfStudy: 'Computer Science',
                startDate: new Date('2008-08-01'),
                endDate: new Date('2012-05-01'),
                isCurrentlyStudying: false,
                grade: 'First Class'
            }],
            workExperience: [{
                company: 'Tech Corp',
                position: 'Software Developer',
                startDate: new Date('2012-06-01'),
                endDate: new Date('2024-01-01'),
                isCurrentlyWorking: false,
                description: 'Developed web applications using modern technologies',
                skills: ['JavaScript', 'React', 'Node.js']
            }],
            skills: [{
                name: 'JavaScript',
                level: 'expert',
                yearsOfExperience: 5,
                certified: true
            }, {
                name: 'React',
                level: 'advanced',
                yearsOfExperience: 3,
                certified: false
            }],
            languages: ['English', 'Hindi'],
            certifications: ['AWS Certified Developer'],
            portfolioLinks: ['https://github.com/testseeker', 'https://testseeker.dev'],
            expectedSalary: {
                min: 800000,
                max: 1200000,
                currency: 'INR',
                period: 'yearly'
            },
            availability: {
                startDate: new Date(),
                noticePeriod: 30,
                workingHours: 'full-time'
            },
            preferences: {
                jobTypes: ['full-time', 'contract'],
                industries: ['technology', 'finance'],
                locations: ['Mumbai', 'Bangalore'],
                remoteWork: true,
                willingToRelocate: false
            },
            isActive: true,
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const seekerResult = await db.collection('seekers').insertOne(seekerData);
        console.log(`   ✅ Created seeker profile: ${seekerResult.insertedId}`);
        
        // Update auth session with seeker ID
        await db.collection('authSessions').updateOne(
            { appwriteId: 'seeker_test_456' },
            { $set: { seekerId: seekerResult.insertedId } }
        );
        
        // Test 3: Create Company from Provider
        console.log('\n🏢 Test 3: Company Creation from Provider');
        
        const companyData = {
            providerId: providerResult.insertedId,
            companyName: 'Test Provider Company',
            description: 'A test company created from provider profile',
            industry: 'technology',
            companySize: 'medium',
            website: providerData.companyAddress.website,
            email: providerData.companyAddress.companyEmail,
            phone: providerData.companyAddress.companyPhone,
            location: {
                latitude: 19.0760,
                longitude: 72.8777,
                address: `${providerData.companyAddress.fullOperationalAddress}, ${providerData.companyAddress.city}, ${providerData.companyAddress.state}, ${providerData.companyAddress.country} - ${providerData.companyAddress.pincode}`
            },
            logo: providerData.branding.companyLogo,
            registrationNumber: providerData.branding.businessRegistrationNumber,
            taxId: providerData.branding.gstinOrMsmeId,
            socialMedia: providerData.companyAddress.socialMediaLinks,
            isVerified: true,
            isActive: true,
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const companyResult = await db.collection('companies').insertOne(companyData);
        console.log(`   ✅ Created company: ${companyResult.insertedId}`);
        
        // Test 4: Create Gig
        console.log('\n💼 Test 4: Gig Creation');
        
        const gigData = {
            providerId: providerResult.insertedId,
            companyId: companyResult.insertedId,
            title: 'Test Software Developer Job',
            description: 'Looking for an experienced software developer',
            jobType: 'full-time',
            workType: 'hybrid',
            location: {
                latitude: 19.0760,
                longitude: 72.8777,
                address: 'Mumbai, Maharashtra, India'
            },
            salary: {
                min: 800000,
                max: 1200000,
                period: 'yearly',
                currency: 'INR'
            },
            requirements: {
                minimumExperience: 3,
                minimumEducation: 'bachelor',
                skills: ['JavaScript', 'React', 'Node.js'],
                languages: ['English']
            },
            benefits: ['Health Insurance', 'Flexible Hours', 'Remote Work'],
            applicationDeadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            numberOfOpenPositions: 2,
            numberOfPeopleApplied: 0,
            companyName: companyData.companyName,
            positionOffered: 'Software Developer',
            minimumQualificationRequired: 'Bachelor\'s degree in Computer Science',
            experience: {
                min: 3,
                max: 7
            },
            isActive: true,
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const gigResult = await db.collection('gigs').insertOne(gigData);
        console.log(`   ✅ Created gig: ${gigResult.insertedId}`);
        
        // Test 5: Create Gig Application
        console.log('\n📝 Test 5: Gig Application Creation');
        
        const applicationData = {
            seekerId: seekerResult.insertedId,
            gigId: gigResult.insertedId,
            appliedAt: new Date(),
            status: 'pending',
            files: ['resume_file_id', 'cover_letter_file_id'],
            coverLetter: 'Test application cover letter for the software developer position',
            expectedSalary: {
                amount: 1000000,
                currency: 'INR',
                period: 'yearly'
            },
            availableStartDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const applicationResult = await db.collection('gig_applications').insertOne(applicationData);
        console.log(`   ✅ Created gig application: ${applicationResult.insertedId}`);
        
        // Test 6: Create FLN Score for Seeker
        console.log('\n📊 Test 6: FLN Score Creation');
        
        const flnData = {
            seekerId: seekerResult.insertedId,
            flnScore: 85,
            details: {
                fluency: 90,
                literacy: 85,
                numeracy: 80
            },
            fluencyRecord: [{
                assessmentDate: new Date(),
                fluency: 90,
                literacy: 85,
                numeracy: 80,
                overallScore: 85,
                assessmentType: 'initial',
                validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
            }],
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const flnResult = await db.collection('fln').insertOne(flnData);
        console.log(`   ✅ Created FLN score: ${flnResult.insertedId}`);
        
        // Test 7: Validation and Relationships
        console.log('\n🔍 Test 7: Validation and Relationships');
        
        // Check auth sessions
        const authSessions = await db.collection('authSessions').find({}).toArray();
        console.log(`   Auth Sessions: ${authSessions.length}`);
        
        // Check provider-company relationship
        const providerCompanies = await db.collection('companies').find({ 
            providerId: providerResult.insertedId 
        }).toArray();
        console.log(`   Provider has ${providerCompanies.length} companies`);
        
        // Check company-gig relationship
        const companyGigs = await db.collection('gigs').find({ 
            companyId: companyResult.insertedId 
        }).toArray();
        console.log(`   Company has ${companyGigs.length} gigs`);
        
        // Check seeker-application relationship
        const seekerApplications = await db.collection('gig_applications').find({ 
            seekerId: seekerResult.insertedId 
        }).toArray();
        console.log(`   Seeker has ${seekerApplications.length} applications`);
        
        // Check seeker-fln relationship
        const seekerFln = await db.collection('fln').find({ 
            seekerId: seekerResult.insertedId 
        }).toArray();
        console.log(`   Seeker has ${seekerFln.length} FLN scores`);
        
        // Validate all relationships
        const allRelationshipsValid = 
            authSessions.length === 2 &&
            providerCompanies.length === 1 &&
            companyGigs.length === 1 &&
            seekerApplications.length === 1 &&
            seekerFln.length === 1;
        
        console.log(`   ✅ All relationships valid: ${allRelationshipsValid ? 'PASS' : 'FAIL'}`);
        
        // Summary
        console.log('\n🎯 New Structure Test Summary:');
        console.log(`   ✅ Provider profile with direct auth: PASS`);
        console.log(`   ✅ Seeker profile with direct auth: PASS`);
        console.log(`   ✅ Company linked to provider_id: PASS`);
        console.log(`   ✅ Gig linked to provider_id: PASS`);
        console.log(`   ✅ Application linked to seeker_id: PASS`);
        console.log(`   ✅ FLN score linked to seeker_id: PASS`);
        console.log(`   ✅ All relationships working: ${allRelationshipsValid ? 'PASS' : 'FAIL'}`);
        
        console.log('\n🎉 New seeker/provider structure testing completed!');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        await client.close();
        console.log('\n🔌 Connection closed.');
    }
}

testNewStructure();
