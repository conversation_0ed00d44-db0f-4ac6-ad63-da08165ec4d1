const { MongoClient } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGO_URI || "mongodb+srv://amriteshk778:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function cleanupOldData() {
    const client = new MongoClient(uri);
    
    try {
        console.log('🔄 Connecting to MongoDB...');
        await client.connect();
        console.log('✅ Connected successfully to MongoDB!');
        
        const db = client.db('giggle');
        
        console.log('\n🧹 Cleaning up old data structure...');
        
        // Remove old user collection if it exists
        try {
            await db.collection('users').drop();
            console.log('   ✅ Dropped old users collection');
        } catch (error) {
            console.log('   ℹ️  Users collection does not exist (already cleaned)');
        }
        
        // Remove providers with userId field (old structure)
        const providersWithUserId = await db.collection('providers').find({ userId: { $exists: true } }).toArray();
        console.log(`   Found ${providersWithUserId.length} providers with old userId structure`);
        
        if (providersWithUserId.length > 0) {
            const deleteResult = await db.collection('providers').deleteMany({ userId: { $exists: true } });
            console.log(`   ✅ Removed ${deleteResult.deletedCount} old provider records`);
        }
        
        // Remove seekers with userId field (old structure)
        const seekersWithUserId = await db.collection('seekers').find({ userId: { $exists: true } }).toArray();
        console.log(`   Found ${seekersWithUserId.length} seekers with old userId structure`);
        
        if (seekersWithUserId.length > 0) {
            const deleteResult = await db.collection('seekers').deleteMany({ userId: { $exists: true } });
            console.log(`   ✅ Removed ${deleteResult.deletedCount} old seeker records`);
        }
        
        // Remove companies with userId field (old structure)
        const companiesWithUserId = await db.collection('companies').find({ userId: { $exists: true } }).toArray();
        console.log(`   Found ${companiesWithUserId.length} companies with old userId structure`);
        
        if (companiesWithUserId.length > 0) {
            const deleteResult = await db.collection('companies').deleteMany({ userId: { $exists: true } });
            console.log(`   ✅ Removed ${deleteResult.deletedCount} old company records`);
        }
        
        // Remove gigs with userId field (old structure)
        const gigsWithUserId = await db.collection('gigs').find({ userId: { $exists: true } }).toArray();
        console.log(`   Found ${gigsWithUserId.length} gigs with old userId structure`);
        
        if (gigsWithUserId.length > 0) {
            const deleteResult = await db.collection('gigs').deleteMany({ userId: { $exists: true } });
            console.log(`   ✅ Removed ${deleteResult.deletedCount} old gig records`);
        }
        
        // Remove gig applications with userId field (old structure)
        const applicationsWithUserId = await db.collection('gig_applications').find({ userId: { $exists: true } }).toArray();
        console.log(`   Found ${applicationsWithUserId.length} applications with old userId structure`);
        
        if (applicationsWithUserId.length > 0) {
            const deleteResult = await db.collection('gig_applications').deleteMany({ userId: { $exists: true } });
            console.log(`   ✅ Removed ${deleteResult.deletedCount} old application records`);
        }
        
        // Remove FLN scores with userId field (old structure)
        const flnWithUserId = await db.collection('fln').find({ userId: { $exists: true } }).toArray();
        console.log(`   Found ${flnWithUserId.length} FLN records with old userId structure`);
        
        if (flnWithUserId.length > 0) {
            const deleteResult = await db.collection('fln').deleteMany({ userId: { $exists: true } });
            console.log(`   ✅ Removed ${deleteResult.deletedCount} old FLN records`);
        }
        
        // Verify cleanup
        console.log('\n🔍 Verifying cleanup...');
        
        const remainingProvidersWithUserId = await db.collection('providers').countDocuments({ userId: { $exists: true } });
        const remainingSeekersWithUserId = await db.collection('seekers').countDocuments({ userId: { $exists: true } });
        const remainingCompaniesWithUserId = await db.collection('companies').countDocuments({ userId: { $exists: true } });
        const remainingGigsWithUserId = await db.collection('gigs').countDocuments({ userId: { $exists: true } });
        const remainingApplicationsWithUserId = await db.collection('gig_applications').countDocuments({ userId: { $exists: true } });
        const remainingFlnWithUserId = await db.collection('fln').countDocuments({ userId: { $exists: true } });
        
        console.log(`   Providers with userId: ${remainingProvidersWithUserId}`);
        console.log(`   Seekers with userId: ${remainingSeekersWithUserId}`);
        console.log(`   Companies with userId: ${remainingCompaniesWithUserId}`);
        console.log(`   Gigs with userId: ${remainingGigsWithUserId}`);
        console.log(`   Applications with userId: ${remainingApplicationsWithUserId}`);
        console.log(`   FLN with userId: ${remainingFlnWithUserId}`);
        
        const allClean = remainingProvidersWithUserId === 0 && 
                        remainingSeekersWithUserId === 0 && 
                        remainingCompaniesWithUserId === 0 && 
                        remainingGigsWithUserId === 0 && 
                        remainingApplicationsWithUserId === 0 && 
                        remainingFlnWithUserId === 0;
        
        console.log(`\n   ✅ Database cleanup: ${allClean ? 'COMPLETE' : 'INCOMPLETE'}`);
        
        // Show current data structure
        console.log('\n📊 Current data structure:');
        const authSessionsCount = await db.collection('authSessions').countDocuments({});
        const providersCount = await db.collection('providers').countDocuments({ isDeleted: false });
        const seekersCount = await db.collection('seekers').countDocuments({ isDeleted: false });
        const companiesCount = await db.collection('companies').countDocuments({ isDeleted: false });
        const gigsCount = await db.collection('gigs').countDocuments({ isDeleted: false });
        const applicationsCount = await db.collection('gig_applications').countDocuments({ isDeleted: false });
        const flnCount = await db.collection('fln').countDocuments({});
        
        console.log(`   Auth Sessions: ${authSessionsCount}`);
        console.log(`   Providers: ${providersCount}`);
        console.log(`   Seekers: ${seekersCount}`);
        console.log(`   Companies: ${companiesCount}`);
        console.log(`   Gigs: ${gigsCount}`);
        console.log(`   Applications: ${applicationsCount}`);
        console.log(`   FLN Scores: ${flnCount}`);
        
        console.log('\n🎉 Database cleanup completed!');
        
    } catch (error) {
        console.error('❌ Cleanup failed:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        await client.close();
        console.log('\n🔌 Connection closed.');
    }
}

cleanupOldData();
