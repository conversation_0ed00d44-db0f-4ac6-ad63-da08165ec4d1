{"scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js"}, "dependencies": {"appwrite": "^18.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "node-appwrite": "^17.0.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}