import { NextFunction, Request, Response } from "express";
import { findById } from "../services/user.service";
import { verifyAppwriteUser } from "../utils/verifyAppwriteUser";

// Wrapper to catch async errors
export const authenticateUser = async (
	req: Request,
	res: Response,
	next: NextFunction
): Promise<void> => {
	try {
		const authHeader = req.headers.authorization;

		if (!authHeader?.startsWith("Bearer ")) {
			res.status(401).json({ error: "Missing or invalid token" });
			return;
		}

		const token = authHeader.split(" ")[1];
		console.log("Received token:", token);
		if (!token) {
			res.status(401).json({ error: "Missing token" });
			return;
		}
		const user = await verifyAppwriteUser(token);
		console.log("Authenticated user:", user);

		if (!user) {
			res.status(401).json({ error: "Invalid or expired token" });
			return;
		}

		const userDetails = await findById(user.$id);
		if (!userDetails) {
			res.status(404).json({ error: "User not found" });
			return;
		}
		console.log("User details found:", userDetails);

		req.user = {
			id: user.$id,
			type: userDetails.userType,
			email: user.email,
		};
		console.log("User set in request:", req.user);
		next();
	} catch (err) {
		console.error(err);
		res.status(500).json({ error: "Internal server error" });
	}
};
