import { ObjectId } from "mongodb";
import { getCollection } from "../config/db";
import { Fln, CreateFlnDto, UpdateFlnDto, FlnRecord } from "../models/fln.model";

export const createFlnRecord = async (flnData: CreateFlnDto): Promise<Fln> => {
	// Check if user already has an FLN record
	const existingFln = await findByUserId(flnData.userId);
	
	if (existingFln) {
		// Update existing record with new assessment
		return await addFlnAssessment(flnData.userId, {
			score: flnData.flnScore,
			details: flnData.details,
			testDate: flnData.testDate || new Date(),
			validUntil: flnData.validUntil,
		});
	}

	// Create new FLN record
	const newFln: Fln = {
		userId: new ObjectId(flnData.userId),
		flnScore: flnData.flnScore,
		details: flnData.details,
		fluencyRecord: [{
			score: flnData.flnScore,
			details: flnData.details,
			testDate: flnData.testDate || new Date(),
			validUntil: flnData.validUntil,
		}],
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await getCollection.fln().insertOne(newFln);
	return { ...newFln, _id: result.insertedId.toString() };
};

export const findByUserId = async (userId: string): Promise<Fln | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}
	return await getCollection.fln().findOne({
		userId: new ObjectId(userId),
		isDeleted: false,
	});
};

export const addFlnAssessment = async (
	userId: string,
	newRecord: FlnRecord
): Promise<Fln> => {
	if (!ObjectId.isValid(userId)) {
		throw new Error("Invalid user ID");
	}

	const updateData = {
		$set: {
			flnScore: newRecord.score,
			details: newRecord.details,
			updatedAt: new Date(),
		},
		$push: {
			fluencyRecord: newRecord,
		},
	};

	const result = await getCollection.fln().findOneAndUpdate(
		{ userId: new ObjectId(userId), isDeleted: false },
		updateData,
		{ returnDocument: "after" }
	);

	if (!result) {
		throw new Error("FLN record not found");
	}

	return result;
};

export const getFlnHistory = async (userId: string): Promise<FlnRecord[]> => {
	const flnRecord = await findByUserId(userId);
	return flnRecord?.fluencyRecord || [];
};

export const getLatestFlnScore = async (userId: string): Promise<number | null> => {
	const flnRecord = await findByUserId(userId);
	return flnRecord?.flnScore || null;
};

export const updateFlnRecord = async (
	userId: string,
	updateData: UpdateFlnDto
): Promise<Fln | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}

	const updateQuery: any = {
		$set: {
			updatedAt: new Date(),
		},
	};

	// Update current score and details if provided
	if (updateData.flnScore !== undefined) {
		updateQuery.$set.flnScore = updateData.flnScore;
	}
	if (updateData.details) {
		updateQuery.$set.details = updateData.details;
	}

	// Add new record to history if provided
	if (updateData.newRecord) {
		updateQuery.$push = {
			fluencyRecord: updateData.newRecord,
		};
		// Update current score to match the new record
		updateQuery.$set.flnScore = updateData.newRecord.score;
		updateQuery.$set.details = updateData.newRecord.details;
	}

	const result = await getCollection.fln().findOneAndUpdate(
		{ userId: new ObjectId(userId), isDeleted: false },
		updateQuery,
		{ returnDocument: "after" }
	);

	return result;
};

export const deleteFlnRecord = async (userId: string): Promise<boolean> => {
	if (!ObjectId.isValid(userId)) {
		return false;
	}

	const result = await getCollection.fln().updateOne(
		{ userId: new ObjectId(userId) },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const getUsersWithMinimumFlnScore = async (
	minimumScore: number,
	limit: number = 50
): Promise<Fln[]> => {
	return await getCollection.fln()
		.find({
			flnScore: { $gte: minimumScore },
			isDeleted: false,
		})
		.limit(limit)
		.toArray();
};

export const getFlnStatistics = async (): Promise<{
	averageScore: number;
	totalAssessments: number;
	scoreDistribution: { range: string; count: number }[];
}> => {
	const pipeline = [
		{ $match: { isDeleted: false } },
		{
			$group: {
				_id: null,
				averageScore: { $avg: "$flnScore" },
				totalAssessments: { $sum: 1 },
				scores: { $push: "$flnScore" },
			},
		},
	];

	const result = await getCollection.fln().aggregate(pipeline).toArray();
	
	if (result.length === 0) {
		return {
			averageScore: 0,
			totalAssessments: 0,
			scoreDistribution: [],
		};
	}

	const data = result[0];
	const scores = data.scores;
	
	// Calculate score distribution
	const scoreDistribution = [
		{ range: "0-20", count: scores.filter((s: number) => s >= 0 && s <= 20).length },
		{ range: "21-40", count: scores.filter((s: number) => s >= 21 && s <= 40).length },
		{ range: "41-60", count: scores.filter((s: number) => s >= 41 && s <= 60).length },
		{ range: "61-80", count: scores.filter((s: number) => s >= 61 && s <= 80).length },
		{ range: "81-100", count: scores.filter((s: number) => s >= 81 && s <= 100).length },
	];

	return {
		averageScore: Math.round(data.averageScore * 100) / 100,
		totalAssessments: data.totalAssessments,
		scoreDistribution,
	};
};
