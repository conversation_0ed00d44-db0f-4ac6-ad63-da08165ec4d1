import { ObjectId } from "mongodb";
import { getCollection } from "../config/db";
import { AuthSession, CreateAuthSessionDto, UpdateAuthSessionDto } from "../models/auth.model";

export class AuthService {
	private static authCollection = () => getCollection.authSessions();

	// Create or update auth session when user logs in
	static async createOrUpdateAuthSession(data: CreateAuthSessionDto): Promise<AuthSession> {
		const collection = this.authCollection();
		
		// Check if auth session already exists for this appwriteId
		const existingSession = await collection.findOne({ appwriteId: data.appwriteId });
		
		if (existingSession) {
			// Update existing session
			const updateData: UpdateAuthSessionDto = {
				selectedProfileType: data.selectedProfileType,
				lastLoginAt: new Date()
			};
			
			await collection.updateOne(
				{ appwriteId: data.appwriteId },
				{ 
					$set: {
						...updateData,
						updatedAt: new Date()
					}
				}
			);
			
			return await collection.findOne({ appwriteId: data.appwriteId }) as AuthSession;
		} else {
			// Create new session
			const newSession: AuthSession = {
				appwriteId: data.appwriteId,
				email: data.email,
				name: data.name,
				selectedProfileType: data.selectedProfileType,
				lastLoginAt: new Date(),
				createdAt: new Date(),
				updatedAt: new Date()
			};
			
			const result = await collection.insertOne(newSession);
			return { ...newSession, _id: result.insertedId };
		}
	}

	// Get auth session by appwriteId
	static async getAuthSession(appwriteId: string): Promise<AuthSession | null> {
		const collection = this.authCollection();
		return await collection.findOne({ appwriteId });
	}

	// Update auth session with profile IDs after profile creation
	static async linkProfileToSession(appwriteId: string, profileType: "seeker" | "provider", profileId: string): Promise<void> {
		const collection = this.authCollection();
		
		const updateData: any = {
			updatedAt: new Date()
		};
		
		if (profileType === "seeker") {
			updateData.seekerId = new ObjectId(profileId);
		} else {
			updateData.providerId = new ObjectId(profileId);
		}
		
		await collection.updateOne(
			{ appwriteId },
			{ $set: updateData }
		);
	}

	// Switch profile type (if user wants to change from seeker to provider or vice versa)
	static async switchProfileType(appwriteId: string, newProfileType: "seeker" | "provider"): Promise<void> {
		const collection = this.authCollection();
		
		await collection.updateOne(
			{ appwriteId },
			{ 
				$set: {
					selectedProfileType: newProfileType,
					lastLoginAt: new Date(),
					updatedAt: new Date()
				}
			}
		);
	}

	// Get user's current profile information
	static async getUserProfile(appwriteId: string): Promise<{
		authSession: AuthSession;
		seekerProfile?: any;
		providerProfile?: any;
	} | null> {
		const authSession = await this.getAuthSession(appwriteId);
		if (!authSession) return null;

		const result: any = { authSession };

		// Get seeker profile if exists
		if (authSession.seekerId) {
			const seekerCollection = getCollection.seekers();
			result.seekerProfile = await seekerCollection.findOne({ _id: authSession.seekerId });
		}

		// Get provider profile if exists
		if (authSession.providerId) {
			const providerCollection = getCollection.providers();
			result.providerProfile = await providerCollection.findOne({ _id: authSession.providerId });
		}

		return result;
	}

	// Delete auth session (logout)
	static async deleteAuthSession(appwriteId: string): Promise<void> {
		const collection = this.authCollection();
		await collection.deleteOne({ appwriteId });
	}

	// Get all auth sessions (admin function)
	static async getAllAuthSessions(): Promise<AuthSession[]> {
		const collection = this.authCollection();
		return await collection.find({}).toArray();
	}
}
