import { ObjectId } from "mongodb";
import { getCollection } from "../config/db";
import {
	Provider,
	CreateProviderPersonalAccountDto,
	UpdateProviderCompanyAddressDto,
	UpdateProviderBrandingDto,
	UpdateProviderDto,
	VerifyProviderPhoneDto,
	RequestVerificationDto,
	calculateProfileCompletion,
	getNextRequiredStep,
	isProfileComplete
} from "../models/provider.model";

// Page 1: Create Personal Account
export const createProviderPersonalAccount = async (
	providerData: CreateProviderPersonalAccountDto
): Promise<Provider> => {
	// Check if provider already exists for this user
	const existingProvider = await findByUserId(providerData.userId);
	if (existingProvider) {
		throw new Error("Provider profile already exists for this user");
	}

	const newProvider: Provider = {
		userId: new ObjectId(providerData.userId),
		phoneNumber: providerData.phoneNumber,
		phoneVerified: false,
		roleTitle: providerData.roleTitle,
		profilePhoto: providerData.profilePhoto,
		termsAccepted: providerData.termsAccepted,
		privacyPolicyAccepted: providerData.privacyPolicyAccepted,
		verificationStatus: "pending",
		profileCompletionSteps: {
			personalAccount: true,
			companyAddress: false,
			brandingVerification: false,
		},
		profileCompletionPercentage: 33, // 1/3 steps completed
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await getCollection.providers().insertOne(newProvider);
	return { ...newProvider, _id: result.insertedId.toString() };
};

// Page 2: Update Company Address
export const updateProviderCompanyAddress = async (
	userId: string,
	addressData: UpdateProviderCompanyAddressDto
): Promise<Provider | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}

	const provider = await findByUserId(userId);
	if (!provider) {
		throw new Error("Provider not found");
	}

	const updatedSteps = {
		...provider.profileCompletionSteps,
		companyAddress: true,
	};

	const updateData = {
		$set: {
			companyAddress: addressData.companyAddress,
			profileCompletionSteps: updatedSteps,
			profileCompletionPercentage: calculateProfileCompletion({
				...provider,
				profileCompletionSteps: updatedSteps,
			}),
			updatedAt: new Date(),
		},
	};

	const result = await getCollection.providers().findOneAndUpdate(
		{ userId: new ObjectId(userId), isDeleted: false },
		updateData,
		{ returnDocument: "after" }
	);

	return result;
};

// Page 3: Update Branding & Verification
export const updateProviderBranding = async (
	userId: string,
	brandingData: UpdateProviderBrandingDto
): Promise<Provider | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}

	const provider = await findByUserId(userId);
	if (!provider) {
		throw new Error("Provider not found");
	}

	const updatedSteps = {
		...provider.profileCompletionSteps,
		brandingVerification: true,
	};

	const updateData = {
		$set: {
			branding: brandingData.branding,
			profileCompletionSteps: updatedSteps,
			profileCompletionPercentage: calculateProfileCompletion({
				...provider,
				profileCompletionSteps: updatedSteps,
			}),
			updatedAt: new Date(),
		},
	};

	// If requesting verification, update status
	if (brandingData.branding.requestVerifiedBadge) {
		updateData.$set.verificationStatus = "in_review";
		updateData.$set.verificationRequestedAt = new Date();
	}

	const result = await getCollection.providers().findOneAndUpdate(
		{ userId: new ObjectId(userId), isDeleted: false },
		updateData,
		{ returnDocument: "after" }
	);

	return result;
};

// Phone verification
export const verifyProviderPhone = async (
	userId: string,
	verificationData: VerifyProviderPhoneDto
): Promise<Provider | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}

	// In a real implementation, you would verify the OTP here
	// For now, we'll just mark as verified

	const updateData = {
		$set: {
			phoneVerified: true,
			otpVerifiedAt: new Date(),
			updatedAt: new Date(),
		},
	};

	const result = await getCollection.providers().findOneAndUpdate(
		{ userId: new ObjectId(userId), phoneNumber: verificationData.phoneNumber, isDeleted: false },
		updateData,
		{ returnDocument: "after" }
	);

	return result;
};

// Find provider by user ID
export const findByUserId = async (userId: string): Promise<Provider | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}
	return await getCollection.providers().findOne({
		userId: new ObjectId(userId),
		isDeleted: false,
	});
};

// Find provider by ID
export const findById = async (providerId: string): Promise<Provider | null> => {
	if (!ObjectId.isValid(providerId)) {
		return null;
	}
	return await getCollection.providers().findOne({
		_id: new ObjectId(providerId),
		isDeleted: false,
	});
};

// Get provider profile completion status
export const getProfileCompletionStatus = async (userId: string) => {
	const provider = await findByUserId(userId);
	if (!provider) {
		return null;
	}

	return {
		completionPercentage: provider.profileCompletionPercentage,
		completedSteps: provider.profileCompletionSteps,
		nextRequiredStep: getNextRequiredStep(provider),
		isComplete: isProfileComplete(provider),
		verificationStatus: provider.verificationStatus,
	};
};

// Update provider
export const updateProvider = async (
	userId: string,
	updateData: UpdateProviderDto
): Promise<Provider | null> => {
	if (!ObjectId.isValid(userId)) {
		return null;
	}

	const updatedProvider = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	const result = await getCollection.providers().findOneAndUpdate(
		{ userId: new ObjectId(userId), isDeleted: false },
		updatedProvider,
		{ returnDocument: "after" }
	);

	return result;
};

// Request verification
export const requestVerification = async (
	userId: string,
	requestData: RequestVerificationDto
): Promise<Provider | null> => {
	const provider = await findByUserId(userId);
	if (!provider) {
		throw new Error("Provider not found");
	}

	if (!isProfileComplete(provider)) {
		throw new Error("Profile must be complete before requesting verification");
	}

	const updateData = {
		$set: {
			"branding.requestVerifiedBadge": requestData.requestVerifiedBadge,
			verificationStatus: "in_review" as const,
			verificationRequestedAt: new Date(),
			updatedAt: new Date(),
		},
	};

	const result = await getCollection.providers().findOneAndUpdate(
		{ userId: new ObjectId(userId), isDeleted: false },
		updateData,
		{ returnDocument: "after" }
	);

	return result;
};

// Admin: Approve/Reject verification
export const updateVerificationStatus = async (
	providerId: string,
	status: "verified" | "rejected",
	notes?: string,
	verifiedBy?: string
): Promise<Provider | null> => {
	if (!ObjectId.isValid(providerId)) {
		return null;
	}

	const updateData = {
		$set: {
			verificationStatus: status,
			verificationCompletedAt: new Date(),
			verificationNotes: notes,
			verifiedBy: verifiedBy ? new ObjectId(verifiedBy) : undefined,
			updatedAt: new Date(),
		},
	};

	const result = await getCollection.providers().findOneAndUpdate(
		{ _id: new ObjectId(providerId), isDeleted: false },
		updateData,
		{ returnDocument: "after" }
	);

	return result;
};

// Get providers pending verification
export const getProvidersForVerification = async (
	limit: number = 20,
	skip: number = 0
): Promise<Provider[]> => {
	return await getCollection.providers()
		.find({
			verificationStatus: "in_review",
			isDeleted: false,
		})
		.sort({ verificationRequestedAt: 1 })
		.skip(skip)
		.limit(limit)
		.toArray();
};

// Delete provider (soft delete)
export const deleteProvider = async (userId: string): Promise<boolean> => {
	if (!ObjectId.isValid(userId)) {
		return false;
	}

	const result = await getCollection.providers().updateOne(
		{ userId: new ObjectId(userId) },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};
