import { ObjectId } from "mongodb";
import { getCollection } from "../config/db";
import {
	Provider,
	CreateProviderPersonalAccountDto,
	UpdateProviderCompanyAddressDto,
	UpdateProviderBrandingDto,
	UpdateProviderDto,
	VerifyProviderPhoneDto,
	RequestVerificationDto,
	calculateProfileCompletion,
	getNextRequiredStep,
	isProfileComplete
} from "../models/provider.model";


export class ProviderService {
	private static providerCollection = () => getCollection.providers();

	// Page 1: Create Personal Account
	static async createPersonalAccount(data: CreateProviderPersonalAccountDto): Promise<Provider> {
		const collection = this.providerCollection();
		
		// Check if provider already exists for this appwriteId
		const existingProvider = await collection.findOne({ 
			appwriteId: data.appwriteId,
			isDeleted: false 
		});
		if (existingProvider) {
			throw new Error("Provider profile already exists for this user");
		}

		const newProvider: Provider = {
			appwriteId: data.appwriteId,
			email: data.email,
			name: data.name,
			phoneNumber: data.phoneNumber,
			phoneVerified: false,
			roleTitle: data.roleTitle,
			profilePhoto: data.profilePhoto,
			termsAccepted: data.termsAccepted,
			privacyPolicyAccepted: data.privacyPolicyAccepted,
			verificationStatus: "pending",
			profileCompletionSteps: {
				personalAccount: true,
				companyAddress: false,
				brandingVerification: false,
			},
			profileCompletionPercentage: 33, // 1/3 steps completed
			isActive: true,
			isDeleted: false,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		const result = await collection.insertOne(newProvider);
		const createdProvider = { ...newProvider, _id: result.insertedId };
		

		
		return createdProvider;
	}

	// Page 2: Update Company Address
	static async updateCompanyAddress(appwriteId: string, addressData: UpdateProviderCompanyAddressDto): Promise<Provider | null> {
		const collection = this.providerCollection();
		
		const provider = await this.findByAppwriteId(appwriteId);
		if (!provider) {
			throw new Error("Provider not found");
		}

		const updatedSteps = {
			...provider.profileCompletionSteps,
			companyAddress: true,
		};

		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					companyAddress: addressData.companyAddress,
					profileCompletionSteps: updatedSteps,
					profileCompletionPercentage: calculateProfileCompletion({
						...provider,
						profileCompletionSteps: updatedSteps,
					}),
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Page 3: Update Branding & Verification
	static async updateBranding(appwriteId: string, brandingData: UpdateProviderBrandingDto): Promise<Provider | null> {
		const collection = this.providerCollection();
		
		const provider = await this.findByAppwriteId(appwriteId);
		if (!provider) {
			throw new Error("Provider not found");
		}

		const updatedSteps = {
			...provider.profileCompletionSteps,
			brandingVerification: true,
		};

		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					branding: brandingData.branding,
					profileCompletionSteps: updatedSteps,
					profileCompletionPercentage: calculateProfileCompletion({
						...provider,
						profileCompletionSteps: updatedSteps,
					}),
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Verify phone number
	static async verifyPhone(appwriteId: string, verificationData: VerifyProviderPhoneDto): Promise<Provider | null> {
		const collection = this.providerCollection();
		
		const provider = await this.findByAppwriteId(appwriteId);
		if (!provider) {
			throw new Error("Provider not found");
		}

		// In a real implementation, you would verify the OTP here
		// For now, we'll just mark as verified
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					phoneVerified: true,
					otpVerifiedAt: new Date(),
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Request verification
	static async requestVerification(appwriteId: string, requestData: RequestVerificationDto): Promise<Provider | null> {
		const collection = this.providerCollection();
		
		const provider = await this.findByAppwriteId(appwriteId);
		if (!provider) {
			throw new Error("Provider not found");
		}

		if (!isProfileComplete(provider)) {
			throw new Error("Profile must be complete before requesting verification");
		}

		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					verificationStatus: "in_review",
					verificationRequestedAt: new Date(),
					verificationNotes: requestData.notes,
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Admin: Approve/Reject verification
	static async updateVerificationStatus(
		providerId: string, 
		status: "verified" | "rejected", 
		adminId: string, 
		notes?: string
	): Promise<Provider | null> {
		const collection = this.providerCollection();
		
		if (!ObjectId.isValid(providerId)) {
			throw new Error("Invalid provider ID");
		}

		const result = await collection.findOneAndUpdate(
			{ _id: new ObjectId(providerId), isDeleted: false },
			{
				$set: {
					verificationStatus: status,
					verificationCompletedAt: new Date(),
					verificationNotes: notes,
					verifiedBy: new ObjectId(adminId),
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Find provider by appwrite ID
	static async findByAppwriteId(appwriteId: string): Promise<Provider | null> {
		const collection = this.providerCollection();
		return await collection.findOne({
			appwriteId: appwriteId,
			isDeleted: false,
		});
	}

	// Find provider by provider ID
	static async findByProviderId(providerId: string): Promise<Provider | null> {
		const collection = this.providerCollection();
		if (!ObjectId.isValid(providerId)) {
			return null;
		}
		return await collection.findOne({
			_id: new ObjectId(providerId),
			isDeleted: false,
		});
	}

	// Get all providers (admin function)
	static async getAllProviders(): Promise<Provider[]> {
		const collection = this.providerCollection();
		return await collection.find({ isDeleted: false }).toArray();
	}

	// Get provider profile completion status
	static async getProfileCompletionStatus(appwriteId: string) {
		const provider = await this.findByAppwriteId(appwriteId);
		if (!provider) {
			return null;
		}

		return {
			provider,
			completionPercentage: provider.profileCompletionPercentage,
			completedSteps: provider.profileCompletionSteps,
			nextStep: getNextRequiredStep(provider),
			isComplete: isProfileComplete(provider),
		};
	}

	// Update provider profile
	static async updateProfile(appwriteId: string, updateData: UpdateProviderDto): Promise<Provider | null> {
		const collection = this.providerCollection();
		
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					...updateData,
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Soft delete provider
	static async deleteProvider(appwriteId: string): Promise<boolean> {
		const collection = this.providerCollection();
		
		const result = await collection.updateOne(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					isDeleted: true,
					isActive: false,
					updatedAt: new Date(),
				},
			}
		);

		return result.matchedCount > 0;
	}
}
