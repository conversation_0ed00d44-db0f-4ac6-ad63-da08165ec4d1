import { ObjectId } from "mongodb";
import { getCollection } from "../config/db";
import { Gig, CreateGigDto, UpdateGigDto } from "../models/gig.model";

export const createGig = async (gigData: CreateGigDto): Promise<Gig> => {
	const newGig: Gig = {
		...gigData,
		providerId: new ObjectId(gigData.providerId),
		numberOfPeopleApplied: 0,
		isActive: true,
		isDeleted: false,
		createdAt: new Date(),
		updatedAt: new Date(),
	};

	const result = await getCollection.gigs().insertOne(newGig);
	return { ...newGig, _id: result.insertedId.toString() };
};

export const findGigById = async (gigId: string): Promise<Gig | null> => {
	if (!ObjectId.isValid(gigId)) {
		return null;
	}
	return await getCollection.gigs().findOne({
		_id: new ObjectId(gigId),
		isDeleted: false,
	});
};

export const findGigsByProvider = async (providerId: string): Promise<Gig[]> => {
	if (!ObjectId.isValid(providerId)) {
		return [];
	}
	return await getCollection.gigs().find({
		providerId: new ObjectId(providerId),
		isDeleted: false,
	}).toArray();
};

export const findActiveGigs = async (
	limit: number = 20,
	skip: number = 0,
	filters?: {
		location?: { latitude: number; longitude: number; radius: number };
		jobType?: string;
		minSalary?: number;
		maxSalary?: number;
		minimumGiggleGrade?: number;
	}
): Promise<Gig[]> => {
	const query: any = {
		isActive: true,
		isDeleted: false,
	};

	// Add filters if provided
	if (filters) {
		if (filters.jobType) {
			query.jobType = filters.jobType;
		}
		if (filters.minSalary) {
			query["salary.min"] = { $gte: filters.minSalary };
		}
		if (filters.maxSalary) {
			query["salary.max"] = { $lte: filters.maxSalary };
		}
		if (filters.minimumGiggleGrade) {
			query.minimumGiggleGrade = { $lte: filters.minimumGiggleGrade };
		}
	}

	return await getCollection.gigs()
		.find(query)
		.sort({ createdAt: -1 })
		.skip(skip)
		.limit(limit)
		.toArray();
};

export const updateGig = async (
	gigId: string,
	updateData: UpdateGigDto
): Promise<Gig | null> => {
	if (!ObjectId.isValid(gigId)) {
		return null;
	}

	const updatedGig = {
		$set: {
			...updateData,
			updatedAt: new Date(),
		},
	};

	const result = await getCollection.gigs().findOneAndUpdate(
		{ _id: new ObjectId(gigId), isDeleted: false },
		updatedGig,
		{ returnDocument: "after" }
	);

	return result;
};

export const deleteGig = async (gigId: string): Promise<boolean> => {
	if (!ObjectId.isValid(gigId)) {
		return false;
	}

	const result = await getCollection.gigs().updateOne(
		{ _id: new ObjectId(gigId) },
		{ $set: { isDeleted: true, updatedAt: new Date() } }
	);

	return result.modifiedCount > 0;
};

export const incrementApplicationCount = async (gigId: string): Promise<boolean> => {
	if (!ObjectId.isValid(gigId)) {
		return false;
	}

	const result = await getCollection.gigs().updateOne(
		{ _id: new ObjectId(gigId), isDeleted: false },
		{ 
			$inc: { numberOfPeopleApplied: 1 },
			$set: { updatedAt: new Date() }
		}
	);

	return result.modifiedCount > 0;
};

export const searchGigs = async (
	searchTerm: string,
	limit: number = 20,
	skip: number = 0
): Promise<Gig[]> => {
	const searchQuery = {
		$and: [
			{ isActive: true, isDeleted: false },
			{
				$or: [
					{ title: { $regex: searchTerm, $options: "i" } },
					{ description: { $regex: searchTerm, $options: "i" } },
					{ companyName: { $regex: searchTerm, $options: "i" } },
					{ positionOffered: { $regex: searchTerm, $options: "i" } },
					{ specialization: { $in: [new RegExp(searchTerm, "i")] } },
				],
			},
		],
	};

	return await getCollection.gigs()
		.find(searchQuery)
		.sort({ createdAt: -1 })
		.skip(skip)
		.limit(limit)
		.toArray();
};
