import { ObjectId } from "mongodb";
import { getCollection } from "../config/db";
import { Seeker, CreateSeekerDto, UpdateSeekerDto } from "../models/seeker.model";


export class SeekerService {
	private static seekerCollection = () => getCollection.seekers();

	// Create seeker profile
	static async createSeeker(data: CreateSeekerDto): Promise<Seeker> {
		const collection = this.seekerCollection();
		
		// Check if seeker already exists for this appwriteId
		const existingSeeker = await collection.findOne({ 
			appwriteId: data.appwriteId,
			isDeleted: false 
		});
		if (existingSeeker) {
			throw new Error("Seeker profile already exists for this user");
		}

		const newSeeker: Seeker = {
			appwriteId: data.appwriteId,
			email: data.email,
			name: data.name,
			phoneNumber: data.phoneNumber,
			phoneVerified: false,
			resumeLink: data.resumeLink,
			resumeFileId: data.resumeFileId,
			educationLevel: data.educationLevel,
			employmentStatus: data.employmentStatus,
			education: data.education || [],
			workExperience: data.workExperience || [],
			skills: data.skills || [],
			languages: data.languages || [],
			certifications: data.certifications || [],
			portfolioLinks: data.portfolioLinks || [],
			expectedSalary: data.expectedSalary,
			availability: data.availability || {
				workingHours: "full-time"
			},
			preferences: data.preferences || {
				remoteWork: false,
				willingToRelocate: false
			},
			isActive: true,
			isDeleted: false,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		const result = await collection.insertOne(newSeeker);
		const createdSeeker = { ...newSeeker, _id: result.insertedId };
		

		
		return createdSeeker;
	}

	// Update seeker profile
	static async updateSeeker(appwriteId: string, updateData: UpdateSeekerDto): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					...updateData,
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Verify phone number
	static async verifyPhone(appwriteId: string, otp: string): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		
		const seeker = await this.findByAppwriteId(appwriteId);
		if (!seeker) {
			throw new Error("Seeker not found");
		}

		// In a real implementation, you would verify the OTP here
		// For now, we'll just mark as verified
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					phoneVerified: true,
					updatedAt: new Date(),
				},
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Add education
	static async addEducation(appwriteId: string, education: any): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$push: { education: education },
				$set: { updatedAt: new Date() }
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Add work experience
	static async addWorkExperience(appwriteId: string, experience: any): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$push: { workExperience: experience },
				$set: { updatedAt: new Date() }
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Add skill
	static async addSkill(appwriteId: string, skill: any): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$push: { skills: skill },
				$set: { updatedAt: new Date() }
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Update job preferences
	static async updateJobPreferences(appwriteId: string, preferences: any): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					preferences: preferences,
					updatedAt: new Date()
				}
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Update availability
	static async updateAvailability(appwriteId: string, availability: any): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		
		const result = await collection.findOneAndUpdate(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					availability: availability,
					updatedAt: new Date()
				}
			},
			{ returnDocument: "after" }
		);

		return result;
	}

	// Find seeker by appwrite ID
	static async findByAppwriteId(appwriteId: string): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		return await collection.findOne({
			appwriteId: appwriteId,
			isDeleted: false,
		});
	}

	// Find seeker by seeker ID
	static async findBySeekerId(seekerId: string): Promise<Seeker | null> {
		const collection = this.seekerCollection();
		if (!ObjectId.isValid(seekerId)) {
			return null;
		}
		return await collection.findOne({
			_id: new ObjectId(seekerId),
			isDeleted: false,
		});
	}

	// Get all seekers (admin function)
	static async getAllSeekers(): Promise<Seeker[]> {
		const collection = this.seekerCollection();
		return await collection.find({ isDeleted: false }).toArray();
	}

	// Search seekers by criteria
	static async searchSeekers(criteria: {
		skills?: string[];
		educationLevel?: string;
		employmentStatus?: string;
		location?: string;
		experienceYears?: number;
	}): Promise<Seeker[]> {
		const collection = this.seekerCollection();
		const query: any = { isDeleted: false };

		if (criteria.skills && criteria.skills.length > 0) {
			query["skills.name"] = { $in: criteria.skills };
		}

		if (criteria.educationLevel) {
			query.educationLevel = criteria.educationLevel;
		}

		if (criteria.employmentStatus) {
			query.employmentStatus = criteria.employmentStatus;
		}

		return await collection.find(query).toArray();
	}

	// Soft delete seeker
	static async deleteSeeker(appwriteId: string): Promise<boolean> {
		const collection = this.seekerCollection();
		
		const result = await collection.updateOne(
			{ appwriteId: appwriteId, isDeleted: false },
			{
				$set: {
					isDeleted: true,
					isActive: false,
					updatedAt: new Date(),
				},
			}
		);

		return result.matchedCount > 0;
	}

	// Get seeker profile completion percentage
	static async getProfileCompletionPercentage(appwriteId: string): Promise<number> {
		const seeker = await this.findByAppwriteId(appwriteId);
		if (!seeker) return 0;

		let completedFields = 0;
		const totalFields = 10; // Adjust based on required fields

		// Check required fields
		if (seeker.name) completedFields++;
		if (seeker.phoneVerified) completedFields++;
		if (seeker.educationLevel) completedFields++;
		if (seeker.employmentStatus) completedFields++;
		if (seeker.education.length > 0) completedFields++;
		if (seeker.skills.length > 0) completedFields++;
		if (seeker.languages.length > 0) completedFields++;
		if (seeker.availability) completedFields++;
		if (seeker.preferences) completedFields++;
		if (seeker.resumeLink || seeker.resumeFileId) completedFields++;

		return Math.round((completedFields / totalFields) * 100);
	}
}
