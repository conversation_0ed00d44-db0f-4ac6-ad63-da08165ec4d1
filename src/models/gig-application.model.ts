import { ObjectId } from "mongodb";

export type ApplicationStatus = "pending" | "rejected" | "shortlisted" | "accepted" | "withdrawn";

export interface GigApplication {
	_id?: string | ObjectId;
	seekerId: string | ObjectId; // Reference to User with userType: "seeker"
	gigId: string | ObjectId; // Reference to Gig
	appliedAt: Date;
	status: ApplicationStatus;
	files: string[]; // Array of file IDs from Files collection
	coverLetter?: string;
	expectedSalary?: {
		amount: number;
		currency: string;
		period: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	availableStartDate?: Date;
	notes?: string; // Internal notes from provider
	reviewedAt?: Date;
	reviewedBy?: string | ObjectId; // Reference to User (provider who reviewed)
	isDeleted: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface CreateGigApplicationDto {
	seekerId: string;
	gigId: string;
	files?: string[];
	coverLetter?: string;
	expectedSalary?: {
		amount: number;
		currency: string;
		period: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	availableStartDate?: Date;
}

export interface UpdateGigApplicationDto {
	status?: ApplicationStatus;
	files?: string[];
	coverLetter?: string;
	expectedSalary?: {
		amount: number;
		currency: string;
		period: "hourly" | "daily" | "weekly" | "monthly" | "yearly";
	};
	availableStartDate?: Date;
	notes?: string;
	reviewedBy?: string;
}

// Helper type for MongoDB document with ObjectId
export type GigApplicationDocument = Omit<GigApplication, "_id"> & {
	_id: ObjectId;
};
