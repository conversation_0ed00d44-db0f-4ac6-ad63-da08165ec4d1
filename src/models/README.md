# MongoDB Models Documentation

This directory contains all the MongoDB models and TypeScript interfaces for the Giggle Backend application.

## Database Structure

The application uses MongoDB with the following collections:

### Core Collections

1. **users** - Base user information for both seekers and providers
2. **providers** - Extended provider profiles with multi-step sign-up flow
3. **gigs** - Job postings created by providers
4. **companies** - Company information linked to providers
5. **seekers** - Extended seeker profiles with detailed information
6. **fln** - Fluency, Literacy, Numeracy scores for users
7. **gig_applications** - Applications submitted by seekers for gigs
8. **files** - File metadata and storage information

## Model Relationships

```
Provider (User) ←→ Company ←→ Gig
                              ↓
Seeker (User) ←→ Seeker Profile → GigApplication
     ↓                              ↑
   FLN Score                    Files
```

### Detailed Relationships

- **Provider ↔ Company**: One-to-many (A provider can have multiple companies)
- **Company ↔ Gig**: One-to-many (A company can post multiple gigs)
- **User ↔ Seeker**: One-to-one (Extended profile for seeker users)
- **User ↔ FLN**: One-to-one (FLN scores for users)
- **Seeker ↔ GigApplication**: One-to-many (A seeker can apply to multiple gigs)
- **Gig ↔ GigApplication**: One-to-many (A gig can have multiple applications)
- **User ↔ Files**: One-to-many (A user can upload multiple files)
- **GigApplication ↔ Files**: Many-to-many (Applications can have multiple files)

## Model Descriptions

### User Model (`user.model.ts`)
Base user information for authentication and basic profile data.
- Supports both "seeker" and "provider" user types
- Includes location data with latitude/longitude
- Soft delete functionality

### Gig Model (`gig.model.ts`)
Job postings with comprehensive job information.
- Salary ranges with different periods
- Location-based jobs
- Job types and traits (remote, on-site, hybrid)
- Minimum qualifications and experience requirements
- FLN grade requirements

### Company Model (`company.model.ts`)
Company profiles linked to providers.
- Industry categorization
- Company size classification
- Social media links
- Verification status

### Seeker Model (`seeker.model.ts`)
Extended profiles for job seekers.
- Education and work experience history
- Skills with proficiency levels
- Job preferences and availability
- Expected salary ranges

### FLN Model (`fln.model.ts`)
Fluency, Literacy, Numeracy assessment scores.
- Historical score tracking
- Detailed breakdown of each component
- Score validity periods

### Gig Application Model (`gig-application.model.ts`)
Applications submitted by seekers.
- Application status tracking
- File attachments
- Cover letters and salary expectations
- Review workflow

### File Model (`file.model.ts`)
File metadata and storage information.
- Support for various file types
- Cloud storage integration
- File categorization and tagging

## Usage Examples

### Importing Models
```typescript
import { User, Gig, Company } from '../models';
// or
import { CreateUserDto, UpdateGigDto } from '../models/user.model';
```

### Using Collection Helpers
```typescript
import { getCollection } from '../config/db';

// Get users collection
const users = getCollection.users();
const user = await users.findOne({ _id: userId });

// Get gigs collection
const gigs = getCollection.gigs();
const activeGigs = await gigs.find({ isActive: true }).toArray();
```

## Environment Configuration

Make sure to set the MongoDB connection string in your environment:

```env
MONGO_URI=mongodb+srv://amriteshk778:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
```

## Data Validation

All models include:
- TypeScript interfaces for type safety
- Create and Update DTOs for API operations
- MongoDB document types with ObjectId
- Soft delete functionality where applicable
- Timestamp tracking (createdAt, updatedAt)

## Best Practices

1. Always use the provided DTOs for create/update operations
2. Use the collection helper functions from `db.ts`
3. Implement proper error handling for database operations
4. Use soft deletes instead of hard deletes
5. Maintain referential integrity between related documents
6. Index frequently queried fields for better performance
