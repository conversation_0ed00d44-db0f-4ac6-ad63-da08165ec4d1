import { ObjectId } from "mongodb";

export interface User {
	_id?: string | ObjectId;
	appwriteId: string;
	email: string;
	name: string;
	userType: "seeker" | "provider"; // Optional field to specify user type
	createdAt: Date;
	updatedAt: Date;
	isDeleted: boolean;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
		// Add any additional profile fields as needed
	};
}

export interface CreateUserDto {
	appwriteId: string;
	email: string;
	name: string;
	userType: string; // Optional field to specify user type
	createdAt?: Date;
	updatedAt?: Date;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
	};
}

export interface UpdateUserDto {
	name?: string;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
	};
}

// Helper type for MongoDB document with ObjectId
export type UserDocument = Omit<User, "_id"> & {
	_id: ObjectId;
};
