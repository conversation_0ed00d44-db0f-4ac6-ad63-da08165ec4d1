import { ObjectId } from "mongodb";

// Location interface for geographical coordinates
export interface Location {
	latitude: number;
	longitude: number;
	address?: string;
}

export interface CompanyAddress {
	city: string;
	state: string;
	country: string;
	fullOperationalAddress: string;
	pincode: string;
	companyEmail: string;
	companyPhone: string;
	website?: string;
	socialMediaLinks?: {
		linkedin?: string;
		instagram?: string;
		facebook?: string;
		twitter?: string;
	};
}

export interface ProviderBranding {
	companyLogo?: string; // URL or file ID
	businessRegistrationNumber?: string;
	gstinOrMsmeId?: string;
	uploadedDocuments?: string[]; // Array of file IDs for licenses, IDs, proof
	requestVerifiedBadge: boolean;
}

export interface ProviderProfile {
	// Page 1: Personal Account
	phoneNumber: string;
	phoneVerified: boolean;
	otpVerifiedAt?: Date;
	roleTitle?: string; // e.g., Franchise Owner, Recruiter
	profilePhoto?: string; // URL or file ID
	termsAccepted: boolean;
	privacyPolicyAccepted: boolean;

	// Page 2: Company Address
	companyAddress?: CompanyAddress;

	// Page 3: Branding & Verification
	branding?: ProviderBranding;

	// Verification status
	verificationStatus: "pending" | "in_review" | "verified" | "rejected";
	verificationRequestedAt?: Date;
	verificationCompletedAt?: Date;
	verificationNotes?: string;

	// Profile completion tracking
	profileCompletionSteps: {
		personalAccount: boolean;
		companyAddress: boolean;
		brandingVerification: boolean;
	};
	profileCompletionPercentage: number; // 0-100
}

export interface User {
	_id?: string | ObjectId;
	appwriteId: string;
	email: string;
	name: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phone?: string;
	location?: Location;
	userType: "seeker" | "provider";
	createdAt: Date;
	updatedAt: Date;
	isDeleted: boolean;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
		// Add any additional profile fields as needed
	};
}

export interface CreateUserDto {
	appwriteId: string;
	email: string;
	name: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phone?: string;
	location?: Location;
	userType: string;
	createdAt?: Date;
	updatedAt?: Date;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
	};
}

export interface UpdateUserDto {
	name?: string;
	dateOfBirth?: Date;
	gender?: "male" | "female" | "other" | "prefer_not_to_say";
	phone?: string;
	location?: Location;
	profile?: {
		bio?: string;
		avatar?: string;
		phoneNumber?: string;
	};
}

// Helper type for MongoDB document with ObjectId
export type UserDocument = Omit<User, "_id"> & {
	_id: ObjectId;
};
