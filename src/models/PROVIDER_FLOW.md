# Provider Sign-Up Flow Documentation

## Overview

The Provider model implements a comprehensive 3-page sign-up flow that guides providers through creating their complete business profile on the Giggle platform.

## Multi-Step Sign-Up Flow

### Page 1: Personal Account Creation
**Purpose**: Create the individual's account who will manage companies/branches

**Fields Required**:
- ✅ Phone Number (with OTP verification)
- ✅ Role Title (optional) - e.g., Franchise Owner, Recruiter
- ✅ Profile Photo (optional)
- ✅ Terms & Conditions acceptance
- ✅ Privacy Policy acceptance

**Database Actions**:
```typescript
// Creates provider record with 33% completion
const provider = await createProviderPersonalAccount({
  userId: "user_id",
  phoneNumber: "+**********",
  roleTitle: "Franchise Owner",
  termsAccepted: true,
  privacyPolicyAccepted: true
});
```

### Page 2: Company Address
**Purpose**: Collect complete business address and contact information

**Fields Required**:
- ✅ City, State, Country
- ✅ Full Operational Address
- ✅ Pincode
- ✅ Company Email Address
- ✅ Company Phone Number
- ✅ Website (optional)
- ✅ Social Media Links (optional) - LinkedIn, Instagram, etc.

**Database Actions**:
```typescript
// Updates provider with address info, 66% completion
await updateProviderCompanyAddress(userId, {
  companyAddress: {
    city: "Mumbai",
    state: "Maharashtra",
    country: "India",
    fullOperationalAddress: "123 Business District",
    pincode: "400069",
    companyEmail: "<EMAIL>",
    companyPhone: "+91-**********",
    website: "https://company.com",
    socialMediaLinks: {
      linkedin: "https://linkedin.com/company/example"
    }
  }
});
```

### Page 3: Branding & Verification
**Purpose**: Upload branding assets and request verification

**Fields Required**:
- ✅ Company Logo
- ✅ Business Registration Number (optional)
- ✅ GSTIN / MSME ID (optional)
- ✅ Upload Documents (optional) - license, ID, proof
- ✅ Request Verified Company badge (checkbox)

**Database Actions**:
```typescript
// Completes profile (100%) and requests verification
await updateProviderBranding(userId, {
  branding: {
    companyLogo: "logo_url",
    businessRegistrationNumber: "REG123456789",
    gstinOrMsmeId: "GSTIN123456789",
    uploadedDocuments: ["doc1_id", "doc2_id"],
    requestVerifiedBadge: true
  }
});
```

## Profile Completion Tracking

The system tracks completion across three steps:

```typescript
interface ProfileCompletionSteps {
  personalAccount: boolean;     // Page 1 completed
  companyAddress: boolean;      // Page 2 completed  
  brandingVerification: boolean; // Page 3 completed
}
```

**Completion Percentages**:
- Page 1 Complete: 33%
- Page 2 Complete: 66%
- Page 3 Complete: 100%

## Verification Workflow

### Provider Verification States:
1. **pending** - Initial state after account creation
2. **in_review** - After requesting verification badge
3. **verified** - Approved by admin
4. **rejected** - Rejected by admin with notes

### Admin Verification Process:
```typescript
// Admin approves verification
await updateVerificationStatus(
  providerId, 
  "verified", 
  "All documents verified successfully",
  adminUserId
);
```

## Phone Verification

Separate OTP verification flow for phone numbers:

```typescript
// Send OTP (implement with SMS service)
await sendOTP(phoneNumber);

// Verify OTP
await verifyProviderPhone(userId, {
  phoneNumber: "+**********",
  otpCode: "123456"
});
```

## Integration with Company Model

Once provider profile is complete, they can create company profiles:

```typescript
// Provider can create multiple companies
const company = await createCompany({
  providerId: provider.userId,
  companyName: "Tech Solutions Inc",
  // ... other company details from provider.companyAddress
});
```

## API Endpoints Structure

**Suggested REST endpoints**:

```
POST /api/providers/personal-account    # Page 1
PUT  /api/providers/company-address     # Page 2  
PUT  /api/providers/branding           # Page 3
POST /api/providers/verify-phone       # Phone verification
GET  /api/providers/completion-status  # Get progress
POST /api/providers/request-verification # Request badge
```

## Database Collections

### providers Collection
```javascript
{
  _id: ObjectId,
  userId: ObjectId,           // Reference to users collection
  phoneNumber: String,
  phoneVerified: Boolean,
  roleTitle: String,
  profilePhoto: String,
  termsAccepted: Boolean,
  privacyPolicyAccepted: Boolean,
  companyAddress: {
    city: String,
    state: String,
    country: String,
    fullOperationalAddress: String,
    pincode: String,
    companyEmail: String,
    companyPhone: String,
    website: String,
    socialMediaLinks: Object
  },
  branding: {
    companyLogo: String,
    businessRegistrationNumber: String,
    gstinOrMsmeId: String,
    uploadedDocuments: [String],
    requestVerifiedBadge: Boolean
  },
  verificationStatus: String,
  profileCompletionSteps: {
    personalAccount: Boolean,
    companyAddress: Boolean,
    brandingVerification: Boolean
  },
  profileCompletionPercentage: Number,
  isActive: Boolean,
  isDeleted: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

## Helper Functions

The model includes utility functions:

```typescript
// Calculate completion percentage
const percentage = calculateProfileCompletion(provider);

// Get next required step
const nextStep = getNextRequiredStep(provider);

// Check if profile is complete
const isComplete = isProfileComplete(provider);
```

## Testing

Run the provider flow test:
```bash
node test-provider-flow.js
```

This will simulate the complete 3-page sign-up flow and verification process.

## Security Considerations

1. **Phone Verification**: Implement proper OTP verification with SMS service
2. **Document Upload**: Validate file types and sizes for uploaded documents
3. **Rate Limiting**: Implement rate limiting for OTP requests
4. **Data Validation**: Validate all input fields on both client and server
5. **Privacy**: Ensure GDPR compliance for data collection

## Next Steps

1. Implement OTP service integration (Twilio, AWS SNS, etc.)
2. Add file upload handling for documents and logos
3. Create admin dashboard for verification management
4. Add email notifications for verification status changes
5. Implement profile completion progress UI components
