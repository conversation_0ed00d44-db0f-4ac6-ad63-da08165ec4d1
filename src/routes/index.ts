import { Express } from "express";

import { authenticateUser } from "../middleware/auth";
import { providerAuthMiddleware } from "../middleware/provider-auth.middleware";
import { seekerAuthMiddleware } from "../middleware/seeker-auth.middleware";
import exampleRoutes from "./example.routes";
import userRoutes from "./user.routes";

export function registerRoutes(app: Express) {
	// Common routes without user type restriction
	app.use("/api/users", userRoutes);

	// Seeker routes with authentication and seeker validation
	app.use("/api/seeker", authenticateUser, seekerAuthMiddleware, exampleRoutes);

	// Provider routes with authentication and provider validation
	app.use(
		"/api/provider",
		authenticateUser,
		providerAuthMiddleware,
		exampleRoutes
	);
}
