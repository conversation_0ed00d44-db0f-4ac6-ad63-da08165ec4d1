import { Collection, Db, MongoClient } from "mongodb";
import dotenv from "dotenv";
import {
	User,
	Gig,
	Fln,
	GigApplication,
	File,
	Company,
	Seeker
} from "../models";

dotenv.config();

// Default to the provided MongoDB cluster URI
const uri = process.env.MONGO_URI || "mongodb+srv://amriteshk778:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const client = new MongoClient(uri);

export let db: Db;

export async function connectToDB() {
	try {
		await client.connect();
		db = client.db("giggle");
		console.log("Connected to MongoDB - Giggle Database");

		// Test the connection
		await db.admin().ping();
		console.log("MongoDB connection verified successfully");
	} catch (err) {
		console.error("DB connection failed", err);
		process.exit(1);
	}
}

export async function closeDBConnection() {
	try {
		await client.close();
		console.log("Disconnected from MongoDB");
	} catch (err) {
		console.error("Error closing DB connection", err);
	}
}

// Collection helper functions
export const getCollection = {
	users: (): Collection<User> => db.collection<User>("users"),
	gigs: (): Collection<Gig> => db.collection<Gig>("gigs"),
	fln: (): Collection<Fln> => db.collection<Fln>("fln"),
	gigApplications: (): Collection<GigApplication> => db.collection<GigApplication>("gig_applications"),
	files: (): Collection<File> => db.collection<File>("files"),
	companies: (): Collection<Company> => db.collection<Company>("companies"),
	seekers: (): Collection<Seeker> => db.collection<Seeker>("seekers")
};
