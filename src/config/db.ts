import { Db, MongoClient } from "mongodb";
import dotenv from "dotenv";
dotenv.config();

const uri = process.env.MONGO_URI || "mongodb://localhost:27017";
const client = new MongoClient(uri);

export let db: Db;

export async function connectToDB() {
	try {
		await client.connect();
		db = client.db("giggle");
		console.log("Connected to MongoDB");
	} catch (err) {
		console.error("DB connection failed", err);
		process.exit(1);
	}
}

export async function closeDBConnection() {
	try {
		await client.close();
		console.log("Disconnected from MongoDB");
	} catch (err) {
		console.error("Error closing DB connection", err);
	}
}
