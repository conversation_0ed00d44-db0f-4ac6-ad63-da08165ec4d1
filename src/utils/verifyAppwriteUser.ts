import { Account, Client } from "node-appwrite";

export async function verifyAppwriteUser(sessionToken: string) {
	const client = new Client()
		.setEndpoint(process.env.APPWRITE_ENDPOINT!)
		.setProject(process.env.APPWRITE_PROJECT_ID!)
		.setSession(sessionToken);

	console.log("client ha bhaiya ", client);

	try {
		const account = new Account(client);
		const user = await account.get();
		return user;
	} catch (err) {
		return null;
	}
}
