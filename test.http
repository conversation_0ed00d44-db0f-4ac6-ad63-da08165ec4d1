@baseUrl = http://localhost:6969

# Variables for environment configuration
@port = 6969
@host = localhost
@apiPrefix = api
@your_jwt_token_here = eyJpZCI6IjY4MzY0NmJlMDAyZGFhZWQwOGIzIiwic2VjcmV0IjoiNjdiY2EwOGFiOGRlNmVmMjU0NWNkNDI0MTVlNDUwZDliMDk3M2VhNGE2OGRlMDE1NWY3MmZkN2RjM2JmM2UzMWYyYmVjMzhiMGI1MzVkMjU5YmMxMTZkNWIyNzczZDNmNDJiODMwZjFkMDU4MjFiMDk5ZDMwMWVkYTUxMjBmNzQxZDU3ODZjMThmM2I1NzVkZDc0NWNhZTY4NzI0MDg2OWM2Njc4ODExNDc3OGFhZTYyZTcxODU2YjQzZmM2MTllZjA4N2JiOWIzZjdjMWVhZmIzODhhZjBmY2NhNDVkNWNkNDcyZjg3ZWFiZmVkZmQxYjU1NjBjZDg4OTk4MjA2ZSJ9

###

# 🛡️ Test protected route with JWT
GET {{baseUrl}}/{{apiPrefix}}/example
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 👤 Register a new user in the backend
POST {{baseUrl}}/{{apiPrefix}}/users/register
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "name": "John Doe",
  "profile": {
    "bio": "Software developer and tech enthusiast",
    "phoneNumber": "+1234567890",
    "avatar": "https://example.com/avatar.jpg"
  }
}

###

# 👤 Get user profile
GET {{baseUrl}}/{{apiPrefix}}/users/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 👤 Update user profile
PATCH {{baseUrl}}/{{apiPrefix}}/users/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

{
  "name": "<PERSON> Updated",
  "profile": {
    "bio": "Senior software developer and tech enthusiast",
    "phoneNumber": "+1234567890"
  }
}

###

# 👤 Delete user (soft delete)
DELETE {{baseUrl}}/{{apiPrefix}}/users/profile
Authorization: Bearer {{your_jwt_token_here}}
Content-Type: application/json

###

# 🚧 Example placeholder for another endpoint
# GET {{host}}:{{port}}/{{apiPrefix}}/users
# Authorization: Bearer {{your_jwt_token_here}}
# Content-Type: application/json
