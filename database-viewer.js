const express = require('express');
const { MongoClient } = require('mongodb');
require('dotenv').config();

const app = express();
const port = 3001;

const uri = process.env.MONGO_URI || "mongodb+srv://amriteshk778:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
const client = new MongoClient(uri);

let db;

// Connect to MongoDB
async function connectDB() {
    try {
        await client.connect();
        db = client.db('giggle');
        console.log('✅ Connected to MongoDB');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error);
    }
}

// HTML template
const htmlTemplate = (title, content) => `
<!DOCTYPE html>
<html>
<head>
    <title>${title}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        h2 { color: #555; margin-top: 30px; }
        .nav { margin: 20px 0; }
        .nav a { margin-right: 15px; padding: 8px 16px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .nav a:hover { background: #0056b3; }
        .collection { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
        .document { background: #f8f9fa; margin: 10px 0; padding: 10px; border-radius: 4px; border-left: 4px solid #007bff; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: #e9ecef; padding: 15px; border-radius: 4px; text-align: center; flex: 1; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Giggle Database Viewer</h1>
        <div class="nav">
            <a href="/">Home</a>
            <a href="/users">Users</a>
            <a href="/gigs">Gigs</a>
            <a href="/companies">Companies</a>
            <a href="/fln">FLN Records</a>
            <a href="/stats">Statistics</a>
        </div>
        ${content}
    </div>
</body>
</html>
`;

// Routes
app.get('/', async (req, res) => {
    try {
        const collections = await db.listCollections().toArray();
        const stats = [];
        
        for (const collection of collections) {
            const count = await db.collection(collection.name).countDocuments();
            stats.push({ name: collection.name, count });
        }
        
        const content = `
            <h2>📊 Database Overview</h2>
            <div class="stats">
                ${stats.map(stat => `
                    <div class="stat-card">
                        <div class="stat-number">${stat.count}</div>
                        <div>${stat.name}</div>
                    </div>
                `).join('')}
            </div>
            
            <h2>📁 Collections</h2>
            ${stats.map(stat => `
                <div class="collection">
                    <h3><a href="/${stat.name}">${stat.name}</a></h3>
                    <p>${stat.count} documents</p>
                </div>
            `).join('')}
        `;
        
        res.send(htmlTemplate('Database Overview', content));
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

app.get('/users', async (req, res) => {
    try {
        const users = await db.collection('users').find({}).toArray();
        const content = `
            <h2>👥 Users (${users.length})</h2>
            ${users.map(user => `
                <div class="document">
                    <h4>${user.name} (${user.userType})</h4>
                    <p><strong>Email:</strong> ${user.email}</p>
                    <p><strong>Phone:</strong> ${user.phone || 'N/A'}</p>
                    <p><strong>Location:</strong> ${user.location?.address || 'N/A'}</p>
                    <p><strong>Created:</strong> ${new Date(user.createdAt).toLocaleDateString()}</p>
                    <details>
                        <summary>View Full Document</summary>
                        <pre>${JSON.stringify(user, null, 2)}</pre>
                    </details>
                </div>
            `).join('')}
        `;
        res.send(htmlTemplate('Users', content));
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

app.get('/gigs', async (req, res) => {
    try {
        const gigs = await db.collection('gigs').find({}).toArray();
        const content = `
            <h2>💼 Gigs (${gigs.length})</h2>
            ${gigs.map(gig => `
                <div class="document">
                    <h4>${gig.title}</h4>
                    <p><strong>Company:</strong> ${gig.companyName}</p>
                    <p><strong>Salary:</strong> $${gig.salary.min.toLocaleString()} - $${gig.salary.max.toLocaleString()} ${gig.salary.period}</p>
                    <p><strong>Type:</strong> ${gig.jobType} (${gig.jobTrait})</p>
                    <p><strong>Applications:</strong> ${gig.numberOfPeopleApplied}</p>
                    <p><strong>Status:</strong> ${gig.isActive ? '🟢 Active' : '🔴 Inactive'}</p>
                    <details>
                        <summary>View Full Document</summary>
                        <pre>${JSON.stringify(gig, null, 2)}</pre>
                    </details>
                </div>
            `).join('')}
        `;
        res.send(htmlTemplate('Gigs', content));
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

app.get('/companies', async (req, res) => {
    try {
        const companies = await db.collection('companies').find({}).toArray();
        const content = `
            <h2>🏢 Companies (${companies.length})</h2>
            ${companies.map(company => `
                <div class="document">
                    <h4>${company.companyName}</h4>
                    <p><strong>Industry:</strong> ${company.industry}</p>
                    <p><strong>Size:</strong> ${company.companySize}</p>
                    <p><strong>Website:</strong> <a href="${company.website}" target="_blank">${company.website}</a></p>
                    <p><strong>Verified:</strong> ${company.isVerified ? '✅ Yes' : '❌ No'}</p>
                    <details>
                        <summary>View Full Document</summary>
                        <pre>${JSON.stringify(company, null, 2)}</pre>
                    </details>
                </div>
            `).join('')}
        `;
        res.send(htmlTemplate('Companies', content));
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

app.get('/fln', async (req, res) => {
    try {
        const flnRecords = await db.collection('fln').find({}).toArray();
        const content = `
            <h2>📊 FLN Records (${flnRecords.length})</h2>
            ${flnRecords.map(fln => `
                <div class="document">
                    <h4>FLN Score: ${fln.flnScore}</h4>
                    <p><strong>Fluency:</strong> ${fln.details.fluency}</p>
                    <p><strong>Literacy:</strong> ${fln.details.literacy}</p>
                    <p><strong>Numeracy:</strong> ${fln.details.numeracy}</p>
                    <p><strong>Assessments:</strong> ${fln.fluencyRecord.length}</p>
                    <details>
                        <summary>View Full Document</summary>
                        <pre>${JSON.stringify(fln, null, 2)}</pre>
                    </details>
                </div>
            `).join('')}
        `;
        res.send(htmlTemplate('FLN Records', content));
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

app.get('/stats', async (req, res) => {
    try {
        const userCount = await db.collection('users').countDocuments();
        const providerCount = await db.collection('users').countDocuments({ userType: 'provider' });
        const seekerCount = await db.collection('users').countDocuments({ userType: 'seeker' });
        const gigCount = await db.collection('gigs').countDocuments();
        const activeGigCount = await db.collection('gigs').countDocuments({ isActive: true });
        const companyCount = await db.collection('companies').countDocuments();
        const flnCount = await db.collection('fln').countDocuments();
        
        const content = `
            <h2>📈 Database Statistics</h2>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">${userCount}</div>
                    <div>Total Users</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${providerCount}</div>
                    <div>Providers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${seekerCount}</div>
                    <div>Seekers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${gigCount}</div>
                    <div>Total Gigs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${activeGigCount}</div>
                    <div>Active Gigs</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${companyCount}</div>
                    <div>Companies</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${flnCount}</div>
                    <div>FLN Records</div>
                </div>
            </div>
        `;
        res.send(htmlTemplate('Statistics', content));
    } catch (error) {
        res.status(500).send(`Error: ${error.message}`);
    }
});

// Start server
connectDB().then(() => {
    app.listen(port, () => {
        console.log(`🌐 Database viewer running at http://localhost:${port}`);
        console.log('📊 View your database in the browser!');
    });
});

process.on('SIGINT', async () => {
    await client.close();
    process.exit(0);
});
