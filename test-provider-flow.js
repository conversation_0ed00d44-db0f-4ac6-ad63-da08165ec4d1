const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGO_URI || "mongodb+srv://amriteshk778:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function testProviderFlow() {
    const client = new MongoClient(uri);
    
    try {
        console.log('🔄 Connecting to MongoDB...');
        await client.connect();
        console.log('✅ Connected successfully to MongoDB!');
        
        const db = client.db('giggle');
        
        console.log('\n📝 Testing Provider Sign-Up Flow...');
        
        // Step 1: Create base user account
        const baseUser = {
            appwriteId: 'provider_flow_test_123',
            email: '<EMAIL>',
            name: 'Provider Flow Test',
            userType: 'provider',
            createdAt: new Date(),
            updatedAt: new Date(),
            isDeleted: false,
            profile: {
                bio: 'Testing provider flow',
                phoneNumber: '+**********'
            }
        };
        
        const userResult = await db.collection('users').insertOne(baseUser);
        console.log(`   ✅ Step 1: Created base user: ${userResult.insertedId}`);
        
        // Step 2: Page 1 - Personal Account
        const providerPersonalAccount = {
            userId: userResult.insertedId,
            phoneNumber: '+**********',
            phoneVerified: false,
            roleTitle: 'Franchise Owner',
            profilePhoto: 'https://example.com/profile.jpg',
            termsAccepted: true,
            privacyPolicyAccepted: true,
            verificationStatus: 'pending',
            profileCompletionSteps: {
                personalAccount: true,
                companyAddress: false,
                brandingVerification: false,
            },
            profileCompletionPercentage: 33,
            isActive: true,
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        
        const providerResult = await db.collection('providers').insertOne(providerPersonalAccount);
        console.log(`   ✅ Step 2: Created provider personal account: ${providerResult.insertedId}`);
        
        // Step 3: Phone Verification
        const phoneVerificationUpdate = {
            $set: {
                phoneVerified: true,
                otpVerifiedAt: new Date(),
                updatedAt: new Date(),
            },
        };
        
        await db.collection('providers').updateOne(
            { _id: providerResult.insertedId },
            phoneVerificationUpdate
        );
        console.log(`   ✅ Step 3: Phone verified`);
        
        // Step 4: Page 2 - Company Address
        const companyAddressUpdate = {
            $set: {
                companyAddress: {
                    city: 'Mumbai',
                    state: 'Maharashtra',
                    country: 'India',
                    fullOperationalAddress: '123 Business District, Andheri East, Mumbai',
                    pincode: '400069',
                    companyEmail: '<EMAIL>',
                    companyPhone: '+91-**********',
                    website: 'https://company.example.com',
                    socialMediaLinks: {
                        linkedin: 'https://linkedin.com/company/example',
                        instagram: 'https://instagram.com/company_example',
                    },
                },
                'profileCompletionSteps.companyAddress': true,
                profileCompletionPercentage: 66,
                updatedAt: new Date(),
            },
        };
        
        await db.collection('providers').updateOne(
            { _id: providerResult.insertedId },
            companyAddressUpdate
        );
        console.log(`   ✅ Step 4: Company address added`);
        
        // Step 5: Page 3 - Branding & Verification
        const brandingUpdate = {
            $set: {
                branding: {
                    companyLogo: 'https://example.com/logo.png',
                    businessRegistrationNumber: 'REG123456789',
                    gstinOrMsmeId: 'GSTIN123456789',
                    uploadedDocuments: ['doc1_file_id', 'doc2_file_id'],
                    requestVerifiedBadge: true,
                },
                'profileCompletionSteps.brandingVerification': true,
                profileCompletionPercentage: 100,
                verificationStatus: 'in_review',
                verificationRequestedAt: new Date(),
                updatedAt: new Date(),
            },
        };
        
        await db.collection('providers').updateOne(
            { _id: providerResult.insertedId },
            brandingUpdate
        );
        console.log(`   ✅ Step 5: Branding & verification request submitted`);
        
        // Step 6: Create Company Profile
        const companyProfile = {
            providerId: userResult.insertedId,
            companyName: 'Test Solutions Pvt Ltd',
            description: 'A leading technology solutions company for testing',
            industry: 'technology',
            companySize: 'medium',
            website: 'https://company.example.com',
            email: '<EMAIL>',
            phone: '+91-**********',
            location: {
                latitude: 19.0760,
                longitude: 72.8777,
                address: 'Mumbai, Maharashtra, India'
            },
            logo: 'https://example.com/logo.png',
            registrationNumber: 'REG123456789',
            taxId: 'GSTIN123456789',
            socialMedia: {
                linkedin: 'https://linkedin.com/company/example',
                instagram: 'https://instagram.com/company_example',
            },
            isVerified: false, // Will be verified after provider verification
            isActive: true,
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const companyResult = await db.collection('companies').insertOne(companyProfile);
        console.log(`   ✅ Step 6: Company profile created: ${companyResult.insertedId}`);
        
        // Step 7: Admin Verification (simulate)
        const verificationUpdate = {
            $set: {
                verificationStatus: 'verified',
                verificationCompletedAt: new Date(),
                verificationNotes: 'All documents verified successfully',
                verifiedBy: new ObjectId(), // Would be admin user ID
                updatedAt: new Date(),
            },
        };
        
        await db.collection('providers').updateOne(
            { _id: providerResult.insertedId },
            verificationUpdate
        );
        
        // Update company verification status
        await db.collection('companies').updateOne(
            { _id: companyResult.insertedId },
            { $set: { isVerified: true, updatedAt: new Date() } }
        );
        
        console.log(`   ✅ Step 7: Provider and company verified by admin`);
        
        // Display final provider profile
        const finalProvider = await db.collection('providers').findOne({ _id: providerResult.insertedId });
        const finalCompany = await db.collection('companies').findOne({ _id: companyResult.insertedId });
        
        console.log('\n📊 Final Provider Profile:');
        console.log('   Provider ID:', finalProvider._id.toString());
        console.log('   Phone Verified:', finalProvider.phoneVerified);
        console.log('   Profile Completion:', finalProvider.profileCompletionPercentage + '%');
        console.log('   Verification Status:', finalProvider.verificationStatus);
        console.log('   Company Name:', finalProvider.companyAddress.companyEmail);
        console.log('   Company Verified:', finalCompany.isVerified);
        
        console.log('\n📋 Profile Completion Steps:');
        console.log('   Personal Account:', finalProvider.profileCompletionSteps.personalAccount ? '✅' : '❌');
        console.log('   Company Address:', finalProvider.profileCompletionSteps.companyAddress ? '✅' : '❌');
        console.log('   Branding & Verification:', finalProvider.profileCompletionSteps.brandingVerification ? '✅' : '❌');
        
        console.log('\n🎉 Provider sign-up flow completed successfully!');
        
        // Show database stats
        console.log('\n📊 Database Collections:');
        const collections = await db.listCollections().toArray();
        for (const collection of collections) {
            const count = await db.collection(collection.name).countDocuments();
            console.log(`   ${collection.name}: ${count} documents`);
        }
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await client.close();
        console.log('\n🔌 Connection closed.');
    }
}

testProviderFlow();
