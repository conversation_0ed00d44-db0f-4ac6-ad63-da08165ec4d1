const { MongoClient } = require('mongodb');
require('dotenv').config();

const uri = process.env.MONGO_URI || "mongodb+srv://amriteshk778:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function testConnection() {
    const client = new MongoClient(uri);
    
    try {
        console.log('🔄 Connecting to MongoDB...');
        await client.connect();
        
        console.log('✅ Connected successfully to MongoDB!');
        
        const db = client.db('giggle');
        
        // List all collections
        console.log('\n📁 Collections in "giggle" database:');
        const collections = await db.listCollections().toArray();
        
        if (collections.length === 0) {
            console.log('   No collections found. Database is empty.');
        } else {
            collections.forEach(collection => {
                console.log(`   - ${collection.name}`);
            });
        }
        
        // Check each collection for document count
        console.log('\n📊 Document counts:');
        for (const collection of collections) {
            const count = await db.collection(collection.name).countDocuments();
            console.log(`   ${collection.name}: ${count} documents`);
        }
        
        // Test ping
        await db.admin().ping();
        console.log('\n🏓 Database ping successful!');
        
    } catch (error) {
        console.error('❌ Connection failed:', error.message);
    } finally {
        await client.close();
        console.log('\n🔌 Connection closed.');
    }
}

testConnection();
